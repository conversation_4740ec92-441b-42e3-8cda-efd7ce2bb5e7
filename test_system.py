#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试财务数据更新系统
"""

import os
import sys
import pandas as pd
from financial_data_updater import Config, DataFetcher, TimeManager, StockClassifier, MissingDataRecovery
from field_mapping import FieldMapper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_api_connection():
    """测试API连接"""
    print("🔗 测试Tushare API连接...")
    
    config = Config()
    try:
        data_fetcher = DataFetcher(config)
        
        # 测试获取股票基本信息
        basic_info = data_fetcher.api.stock_basic(list_status='L', fields='ts_code,symbol,name,industry')
        print(f"✅ API连接成功，获取到 {len(basic_info)} 只股票信息")
        
        # 显示前5只股票
        print("📊 前5只股票信息:")
        for i, row in basic_info.head().iterrows():
            print(f"   {row['ts_code']} - {row['name']} - {row['industry']}")
        
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_stock_classification():
    """测试股票分类功能"""
    print("\n🏷️  测试股票分类功能...")
    
    config = Config()
    data_fetcher = DataFetcher(config)
    classifier = StockClassifier(data_fetcher.api)
    
    # 测试几个不同类型的股票
    test_stocks = [
        ('000001.SZ', '平安银行'),  # 银行
        ('000002.SZ', '万科A'),    # 一般企业
        ('600030.SH', '中信证券'), # 证券
        ('601318.SH', '中国平安')  # 保险
    ]
    
    for ts_code, name in test_stocks:
        try:
            company_type = classifier.classify_stock(ts_code)
            print(f"   {ts_code} ({name}) -> {company_type}")
        except Exception as e:
            print(f"   ❌ {ts_code} 分类失败: {e}")

def test_time_detection():
    """测试时间检测功能"""
    print("\n⏰ 测试时间检测功能...")
    
    config = Config()
    time_manager = TimeManager(config)
    
    # 测试不同的最新日期
    test_dates = [
        20240331,  # 2024年一季报
        20240630,  # 2024年半年报
        20240930,  # 2024年三季报
        20231231   # 2023年年报
    ]
    
    for latest_date in test_dates:
        missing_periods = time_manager.detect_missing_periods(latest_date)
        print(f"   最新数据: {latest_date}")
        if missing_periods:
            for period in missing_periods:
                print(f"     缺失: {period['period']} ({period['type']}) 优先级: {period['priority']}")
        else:
            print("     无缺失数据")
        print()

def test_data_fetching():
    """测试数据获取功能"""
    print("\n📥 测试数据获取功能...")
    
    config = Config()
    data_fetcher = DataFetcher(config)
    
    # 测试获取一般企业数据
    test_stock = '000002.SZ'  # 万科A
    test_period = '20240930'   # 2024年三季报
    
    try:
        print(f"   获取 {test_stock} {test_period} 数据...")
        data = data_fetcher.fetch_financial_data(test_stock, '一般企业', test_period)
        
        if data is not None and not data.empty:
            print(f"   ✅ 成功获取数据，共 {len(data)} 行 {len(data.columns)} 列")
            print(f"   数据列: {list(data.columns)[:10]}...")  # 显示前10列
        else:
            print("   ⚠️  获取到空数据")
            
    except Exception as e:
        print(f"   ❌ 数据获取失败: {e}")

def test_field_mapping():
    """测试字段映射功能"""
    print("\n🗺️  测试字段映射功能...")
    
    mapper = FieldMapper()
    
    # 测试不同公司类型的映射
    company_types = ['一般企业', '商业银行', '证券公司', '保险公司']
    
    for company_type in company_types:
        mapping = mapper.get_mapping_for_company_type(company_type)
        print(f"   {company_type}: {len(mapping)} 个字段映射")
        
        # 显示前5个映射
        sample_mappings = list(mapping.items())[:5]
        for tushare_field, csv_field in sample_mappings:
            print(f"     {tushare_field} -> {csv_field}")
        print()

def test_csv_file_reading():
    """测试CSV文件读取功能"""
    print("\n📄 测试CSV文件读取功能...")
    
    config = Config()
    time_manager = TimeManager(config)
    
    # 查找一个示例CSV文件
    if os.path.exists(config.DATA_PATH):
        stock_folders = [d for d in os.listdir(config.DATA_PATH) 
                        if os.path.isdir(os.path.join(config.DATA_PATH, d))]
        
        if stock_folders:
            # 取第一个股票文件夹
            sample_folder = stock_folders[0]
            csv_files = [f for f in os.listdir(os.path.join(config.DATA_PATH, sample_folder)) 
                        if f.endswith('.csv')]
            
            if csv_files:
                csv_path = os.path.join(config.DATA_PATH, sample_folder, csv_files[0])
                print(f"   测试文件: {csv_path}")
                
                try:
                    latest_date = time_manager.get_latest_date_from_csv(csv_path)
                    print(f"   ✅ 最新报告期: {latest_date}")
                    
                    # 读取文件查看结构
                    df = pd.read_csv(csv_path, skiprows=1)
                    print(f"   文件结构: {len(df)} 行 {len(df.columns)} 列")
                    print(f"   报告期范围: {df['report_date'].min()} - {df['report_date'].max()}")
                    
                except Exception as e:
                    print(f"   ❌ 读取失败: {e}")
            else:
                print("   ⚠️  文件夹中没有CSV文件")
        else:
            print("   ⚠️  没有找到股票文件夹")
    else:
        print(f"   ⚠️  数据目录不存在: {config.DATA_PATH}")

def test_missing_data_detection():
    """测试缺失数据检测功能"""
    print("\n🔍 测试缺失数据检测功能...")
    
    config = Config()
    data_fetcher = DataFetcher(config)
    time_manager = TimeManager(config)
    classifier = StockClassifier(data_fetcher.api)
    recovery_system = MissingDataRecovery(config, data_fetcher, time_manager, classifier)
    
    # 只扫描前5个股票文件夹
    if os.path.exists(config.DATA_PATH):
        stock_folders = [d for d in os.listdir(config.DATA_PATH) 
                        if os.path.isdir(os.path.join(config.DATA_PATH, d))][:5]
        
        print(f"   扫描前 {len(stock_folders)} 个股票文件夹...")
        
        missing_count = 0
        for stock_folder in stock_folders:
            try:
                csv_files = [f for f in os.listdir(os.path.join(config.DATA_PATH, stock_folder)) 
                            if f.endswith('.csv')]
                
                if csv_files:
                    csv_path = os.path.join(config.DATA_PATH, stock_folder, csv_files[0])
                    latest_date = time_manager.get_latest_date_from_csv(csv_path)
                    
                    if latest_date:
                        missing_periods = time_manager.detect_missing_periods(latest_date)
                        if missing_periods:
                            missing_count += 1
                            print(f"   {stock_folder}: 最新 {latest_date}, 缺失 {len(missing_periods)} 期")
                            for period in missing_periods:
                                print(f"     - {period['period']} ({period['type']})")
                        else:
                            print(f"   {stock_folder}: 最新 {latest_date}, 无缺失")
                            
            except Exception as e:
                print(f"   ❌ {stock_folder} 检测失败: {e}")
        
        print(f"   📊 总结: {missing_count}/{len(stock_folders)} 只股票有缺失数据")
    else:
        print(f"   ⚠️  数据目录不存在: {config.DATA_PATH}")

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行系统测试...\n")
    
    tests = [
        test_api_connection,
        test_stock_classification,
        test_time_detection,
        test_data_fetching,
        test_field_mapping,
        test_csv_file_reading,
        test_missing_data_detection
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
            print("✅ 测试通过\n")
        except Exception as e:
            print(f"❌ 测试失败: {e}\n")
    
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🚀 系统测试完成，可以开始正式运行数据更新！")
        
        # 询问是否运行实际更新
        response = input("\n是否运行实际的数据更新？(y/n): ")
        if response.lower() == 'y':
            print("启动主程序...")
            os.system("python financial_data_updater.py")
    else:
        print("\n❌ 请修复测试中发现的问题后再运行。")
