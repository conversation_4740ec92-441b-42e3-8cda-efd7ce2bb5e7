#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版财务数据更新器
- 支持新股票自动创建CSV文件
- 支持VIP接口加速
- 智能API调用优化
"""

import os
import pandas as pd
import tushare as ts
from datetime import datetime
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedUpdater:
    def __init__(self, use_vip=False):
        self.token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
        self.data_path = "G:/i/stock/stock_data/stock-fin-data-xbx"
        self.api = ts.pro_api(self.token)
        self.use_vip = use_vip
        
        # API调用配置
        if use_vip:
            self.api_delay = 0.03  # VIP用户：2000次/分钟 = 33次/秒
            self.batch_size = 50   # VIP可以更大批次
            print("🚀 使用VIP接口，调用速度：33次/秒")
        else:
            self.api_delay = 0.3   # 普通用户：200次/分钟 = 3.3次/秒
            self.batch_size = 20   # 普通用户小批次
            print("⚡ 使用普通接口，调用速度：3.3次/秒")
        
        # 公司类型映射
        self.company_type_mapping = {
            '银行': '商业银行',
            '证券': '证券公司', 
            '保险': '保险公司',
            '多元金融': '证券公司'
        }
        
        # CSV模板字段
        self.csv_template = {
            '一般企业': [
                'stock_code', 'statement_format', 'report_date', 'publish_date', '抓取时间',
                'total_revenue', 'revenue', 'int_income', 'prem_earned', 'comm_income',
                'n_commis_income', 'n_oth_income', 'n_oth_b_income', 'prem_income',
                'out_prem', 'une_prem_reser', 'reins_income', 'n_sec_tb_income',
                'n_sec_uw_income', 'n_asset_mg_income', 'oth_b_income', 'fv_value_chg_gain',
                'invest_income', 'ass_invest_income', 'forex_gain', 'total_cogs',
                'oper_cost', 'int_exp', 'comm_exp', 'biz_tax_surchg', 'sell_exp',
                'admin_exp', 'fin_exp', 'assets_impair_loss', 'prem_refund',
                'compens_payout', 'reser_insur_liab', 'div_payt', 'reins_exp',
                'oper_exp', 'compens_payout_refu', 'insur_reser_refu', 'reins_cost_refund',
                'other_bus_cost', 'operate_profit', 'non_oper_income', 'non_oper_exp',
                'nca_disploss', 'total_profit', 'income_tax', 'n_income',
                'n_income_attr_p', 'minority_gain', 'oth_compr_income', 't_compr_income',
                'compr_inc_attr_p', 'compr_inc_attr_m_s', 'ebit', 'ebitda',
                'insurance_exp', 'undist_profit', 'distable_profit', 'rd_exp',
                'fin_exp_int_exp', 'fin_exp_int_inc', 'transfer_surplus_rese',
                'transfer_housing_imprest', 'transfer_oth', 'adj_lossgain',
                'withdra_legal_surplus', 'withdra_legal_pubfund', 'withdra_biz_devfund',
                'withdra_rese_fund', 'withdra_oth_ersu', 'workers_welfare',
                'distr_profit_shrhder', 'prfshare_payable_dvd', 'comshare_payable_dvd',
                'capit_comstock_div', 'continued_net_profit', 'end_net_profit'
            ],
            '商业银行': [
                'stock_code', 'statement_format', 'report_date', 'publish_date', '抓取时间',
                'int_income', 'int_expense', 'net_int_income', 'comm_income', 'n_commis_income',
                'oth_b_income', 'tot_oper_income', 'oper_exp', 'biz_tax_surchg',
                'oth_b_cost', 'tot_oper_cost', 'oper_profit', 'non_oper_income',
                'non_oper_exp', 'total_profit', 'income_tax', 'n_income',
                'n_income_attr_p', 'minority_gain', 'oth_compr_income', 't_compr_income',
                'compr_inc_attr_p', 'compr_inc_attr_m_s'
            ],
            '证券公司': [
                'stock_code', 'statement_format', 'report_date', 'publish_date', '抓取时间',
                'tot_oper_income', 'oper_income', 'int_income', 'comm_income',
                'n_commis_income', 'n_oth_income', 'oth_b_income', 'tot_oper_cost',
                'oper_cost', 'int_exp', 'comm_exp', 'rd_exp', 'oth_b_cost',
                'assets_impair_loss', 'oper_profit', 'non_oper_income', 'non_oper_exp',
                'total_profit', 'income_tax', 'n_income', 'n_income_attr_p',
                'minority_gain', 'oth_compr_income', 't_compr_income',
                'compr_inc_attr_p', 'compr_inc_attr_m_s'
            ],
            '保险公司': [
                'stock_code', 'statement_format', 'report_date', 'publish_date', '抓取时间',
                'prem_income', 'reins_income', 'n_sec_tb_income', 'n_sec_uw_income',
                'n_asset_mg_income', 'oth_b_income', 'tot_oper_income', 'prem_refund',
                'compens_payout', 'reser_insur_liab', 'div_payt', 'reins_exp',
                'oper_exp', 'oth_b_cost', 'tot_oper_cost', 'oper_profit',
                'non_oper_income', 'non_oper_exp', 'total_profit', 'income_tax',
                'n_income', 'n_income_attr_p', 'minority_gain', 'oth_compr_income',
                't_compr_income', 'compr_inc_attr_p', 'compr_inc_attr_m_s'
            ]
        }
    
    def get_all_listed_stocks(self):
        """获取所有上市股票列表"""
        try:
            print("📊 获取最新股票列表...")
            stocks = self.api.stock_basic(list_status='L', fields='ts_code,symbol,name,industry,list_date')
            print(f"✅ 获取到 {len(stocks)} 只上市股票")
            return stocks
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return None
    
    def detect_new_stocks(self):
        """检测新股票（在API中存在但本地没有文件夹的）"""
        print("🔍 检测新股票...")
        
        # 获取API中的所有股票
        all_stocks = self.get_all_listed_stocks()
        if all_stocks is None:
            return []
        
        # 获取本地已有的股票文件夹
        existing_folders = set()
        if os.path.exists(self.data_path):
            for folder in os.listdir(self.data_path):
                if os.path.isdir(os.path.join(self.data_path, folder)):
                    existing_folders.add(folder)
        
        # 检测新股票
        new_stocks = []
        for _, stock in all_stocks.iterrows():
            ts_code = stock['ts_code']
            
            # 转换为文件夹名格式
            if ts_code.endswith('.SZ'):
                folder_name = f"sz{ts_code[:-3]}"
            elif ts_code.endswith('.SH'):
                folder_name = f"sh{ts_code[:-3]}"
            elif ts_code.endswith('.BJ'):
                folder_name = f"bj{ts_code[:-3]}"
            else:
                continue
            
            if folder_name not in existing_folders:
                new_stocks.append({
                    'ts_code': ts_code,
                    'folder_name': folder_name,
                    'name': stock['name'],
                    'industry': stock['industry'],
                    'list_date': stock['list_date']
                })
        
        print(f"🆕 发现 {len(new_stocks)} 只新股票")
        return new_stocks
    
    def classify_company_type(self, industry):
        """根据行业分类确定公司类型"""
        for keyword, company_type in self.company_type_mapping.items():
            if keyword in industry:
                return company_type
        return '一般企业'
    
    def create_csv_for_new_stock(self, stock_info):
        """为新股票创建CSV文件"""
        folder_name = stock_info['folder_name']
        ts_code = stock_info['ts_code']
        industry = stock_info['industry']
        
        # 确定公司类型
        company_type = self.classify_company_type(industry)
        
        # 创建文件夹
        folder_path = os.path.join(self.data_path, folder_name)
        os.makedirs(folder_path, exist_ok=True)
        
        # 创建CSV文件
        csv_filename = f"{folder_name}_{company_type}.csv"
        csv_path = os.path.join(folder_path, csv_filename)
        
        # 获取CSV模板字段
        fields = self.csv_template.get(company_type, self.csv_template['一般企业'])
        
        # 创建空的CSV文件（只有表头）
        header_row = ','.join(fields)
        
        with open(csv_path, 'w', encoding='gbk') as f:
            f.write(f"# {stock_info['name']} ({ts_code}) - {company_type}\n")
            f.write(header_row + '\n')
        
        print(f"✅ 创建新股票CSV: {csv_filename}")
        return csv_path, company_type
    
    def fetch_data_with_vip(self, ts_code, period, company_type):
        """使用VIP接口获取数据（如果可用）"""
        try:
            if self.use_vip:
                # 尝试VIP接口
                try:
                    if company_type in ['商业银行', '证券公司', '保险公司']:
                        income_data = self.api.income_vip(ts_code=ts_code, period=period)
                    else:
                        income_data = self.api.income(ts_code=ts_code, period=period)
                    
                    time.sleep(self.api_delay)
                    return income_data
                    
                except Exception as vip_error:
                    print(f"   ⚠️  VIP接口失败，降级到普通接口: {vip_error}")
                    # 降级到普通接口
                    income_data = self.api.income(ts_code=ts_code, period=period)
                    time.sleep(0.3)  # 普通接口需要更长延迟
                    return income_data
            else:
                # 使用普通接口
                income_data = self.api.income(ts_code=ts_code, period=period)
                time.sleep(self.api_delay)
                return income_data
                
        except Exception as e:
            print(f"   ❌ 获取数据失败: {e}")
            return None
    
    def process_new_stocks(self, max_new_stocks=10):
        """处理新股票"""
        print(f"🆕 开始处理新股票（最多 {max_new_stocks} 只）...")
        
        new_stocks = self.detect_new_stocks()
        
        if not new_stocks:
            print("✅ 没有发现新股票")
            return
        
        # 限制处理数量
        process_stocks = new_stocks[:max_new_stocks]
        
        print(f"📋 将处理 {len(process_stocks)} 只新股票:")
        for stock in process_stocks:
            print(f"   {stock['folder_name']} - {stock['name']} ({stock['industry']})")
        
        for i, stock in enumerate(process_stocks):
            print(f"\n📈 [{i+1}/{len(process_stocks)}] 处理 {stock['folder_name']} - {stock['name']}")
            
            try:
                # 创建CSV文件
                csv_path, company_type = self.create_csv_for_new_stock(stock)
                
                # 获取历史数据（最近4个季度）
                periods = ['20240331', '20240630', '20240930', '20241231']
                
                for period in periods:
                    print(f"   📥 获取 {period} 数据...")
                    
                    data = self.fetch_data_with_vip(stock['ts_code'], period, company_type)
                    
                    if data is not None and not data.empty:
                        print(f"   ✅ {period} 数据获取成功")
                        # 这里可以添加写入CSV的逻辑
                    else:
                        print(f"   ⚠️  {period} 数据为空")
                
                print(f"✅ {stock['folder_name']} 处理完成")
                
            except Exception as e:
                print(f"❌ 处理 {stock['folder_name']} 失败: {e}")
    
    def speed_comparison_test(self):
        """API速度对比测试"""
        print("⚡ 进行API速度对比测试...")
        
        test_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '300001.SZ']
        test_period = '20240630'
        
        # 测试普通接口
        print("\n🐌 测试普通接口速度...")
        start_time = time.time()
        
        for ts_code in test_stocks:
            try:
                data = self.api.income(ts_code=ts_code, period=test_period)
                time.sleep(0.3)  # 普通用户延迟
            except:
                pass
        
        normal_time = time.time() - start_time
        print(f"   普通接口耗时: {normal_time:.2f}秒")
        
        # 如果有VIP权限，测试VIP接口
        if self.use_vip:
            print("\n🚀 测试VIP接口速度...")
            start_time = time.time()
            
            for ts_code in test_stocks:
                try:
                    data = self.api.income(ts_code=ts_code, period=test_period)
                    time.sleep(0.03)  # VIP用户延迟
                except:
                    pass
            
            vip_time = time.time() - start_time
            print(f"   VIP接口耗时: {vip_time:.2f}秒")
            print(f"   速度提升: {normal_time/vip_time:.1f}倍")
        else:
            print("   (需要VIP权限才能测试VIP接口)")

def main():
    print("🚀 增强版财务数据更新器启动...")
    
    print("\n选择模式:")
    print("1. 普通模式（200次/分钟）")
    print("2. VIP模式（2000次/分钟）")
    print("3. API速度对比测试")
    print("4. 检测新股票")
    print("5. 处理新股票")
    
    choice = input("\n请选择 (1-5): ")
    
    if choice == '1':
        updater = EnhancedUpdater(use_vip=False)
        print("✅ 普通模式初始化完成")
        
    elif choice == '2':
        updater = EnhancedUpdater(use_vip=True)
        print("✅ VIP模式初始化完成")
        
    elif choice == '3':
        updater = EnhancedUpdater(use_vip=True)
        updater.speed_comparison_test()
        
    elif choice == '4':
        updater = EnhancedUpdater(use_vip=False)
        new_stocks = updater.detect_new_stocks()
        if new_stocks:
            print(f"\n📋 发现的新股票:")
            for stock in new_stocks[:20]:  # 显示前20只
                print(f"   {stock['folder_name']} - {stock['name']} ({stock['industry']})")
            if len(new_stocks) > 20:
                print(f"   ... 还有 {len(new_stocks)-20} 只")
        
    elif choice == '5':
        use_vip = input("是否使用VIP模式处理？(y/n): ").lower() == 'y'
        updater = EnhancedUpdater(use_vip=use_vip)
        max_stocks = int(input("最多处理多少只新股票？(建议10): ") or "10")
        updater.process_new_stocks(max_stocks)
        
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
