#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务数据更新系统
使用Tushare API更新股票财务数据
支持不同类型公司的差异化处理和缺失数据补齐
"""

import os
import sys
import time
import glob
import pandas as pd
import tushare as ts
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
import logging
import json
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_updater.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Config:
    """配置类"""
    def __init__(self):
        self.TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
        self.DATA_PATH = "G:/i/stock/stock_data/stock-fin-data-xbx"
        self.BACKUP_PATH = "G:/i/stock/stock_data/backup"
        self.API_DELAY = 0.2  # API调用间隔（秒）
        self.MAX_RETRIES = 3
        self.BATCH_SIZE = 100
        
        # 报告期发布时间表
        self.REPORT_DEADLINES = {
            '0331': (4, 30),   # 一季报：4月30日
            '0630': (8, 31),   # 半年报：8月31日  
            '0930': (10, 31),  # 三季报：10月31日
            '1231': (4, 30)    # 年报：次年4月30日
        }
        
        # 期间优先级
        self.PERIOD_PRIORITY = {
            '1231': 4,  # 年报最高
            '0630': 3,  # 半年报
            '0930': 2,  # 三季报
            '0331': 1   # 一季报最低
        }

class StockClassifier:
    """股票分类器 - 识别公司类型"""
    
    def __init__(self, tushare_api):
        self.api = tushare_api
        self.industry_mapping = {
            '银行': '商业银行',
            '证券': '证券公司',
            '保险': '保险公司'
        }
        self.classification_cache = {}
    
    def classify_stock(self, ts_code: str) -> str:
        """
        分类股票类型
        
        Args:
            ts_code: 股票代码，如 '000001.SZ'
            
        Returns:
            公司类型: '一般企业', '商业银行', '证券公司', '保险公司'
        """
        if ts_code in self.classification_cache:
            return self.classification_cache[ts_code]
        
        try:
            # 获取股票基本信息
            basic_info = self.api.stock_basic(ts_code=ts_code, fields='ts_code,name,industry')
            
            if basic_info.empty:
                logger.warning(f"无法获取 {ts_code} 的基本信息，默认为一般企业")
                company_type = '一般企业'
            else:
                industry = basic_info.iloc[0]['industry']
                company_type = self._map_industry_to_type(industry)
            
            self.classification_cache[ts_code] = company_type
            return company_type
            
        except Exception as e:
            logger.error(f"分类股票 {ts_code} 时出错: {e}")
            return '一般企业'  # 默认返回一般企业
    
    def _map_industry_to_type(self, industry: str) -> str:
        """将行业映射到公司类型"""
        for keyword, company_type in self.industry_mapping.items():
            if keyword in industry:
                return company_type
        return '一般企业'
    
    def classify_from_filename(self, filename: str) -> str:
        """从文件名推断公司类型"""
        if '商业银行' in filename:
            return '商业银行'
        elif '证券公司' in filename:
            return '证券公司'
        elif '保险公司' in filename:
            return '保险公司'
        else:
            return '一般企业'

class TimeManager:
    """时间管理器 - 处理报告期时间逻辑"""
    
    def __init__(self, config: Config):
        self.config = config
        self.period_sequence = ['0331', '0630', '0930', '1231']
    
    def get_latest_date_from_csv(self, csv_file_path: str) -> Optional[int]:
        """从CSV文件获取最新报告期"""
        try:
            # 尝试不同的编码方式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
            df = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file_path, skiprows=1, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
                except Exception:
                    continue

            if df is None:
                logger.warning(f"无法读取CSV文件 {csv_file_path}，尝试了所有编码方式")
                return None

            if 'report_date' in df.columns and not df.empty:
                latest_date = df['report_date'].max()
                return int(latest_date)
            return None
        except Exception as e:
            logger.error(f"读取CSV文件 {csv_file_path} 失败: {e}")
            return None
    
    def detect_missing_periods(self, current_latest_date: int, current_time: datetime = None) -> List[Dict]:
        """
        检测缺失的报告期
        
        Args:
            current_latest_date: 当前最新的报告期，如 20240331
            current_time: 当前时间，默认为系统当前时间
            
        Returns:
            缺失期间列表，每个元素包含 period, type, deadline, priority
        """
        if current_time is None:
            current_time = datetime.now()
        
        # 解析当前最新数据的年份和期间
        latest_year = int(str(current_latest_date)[:4])
        latest_period = str(current_latest_date)[-4:]
        
        missing_periods = []
        
        # 从最新期间开始，逐个检查后续期间
        current_year = latest_year
        current_idx = self.period_sequence.index(latest_period)
        
        while True:
            # 移动到下一个期间
            current_idx += 1
            
            # 如果超过年度，进入下一年
            if current_idx >= len(self.period_sequence):
                current_idx = 0
                current_year += 1
            
            next_period = self.period_sequence[current_idx]
            next_period_date = f"{current_year}{next_period}"
            
            # 计算该期间的发布截止日期
            deadline_month, deadline_day = self.config.REPORT_DEADLINES[next_period]
            
            if next_period == '1231':  # 年报在次年发布
                deadline_year = current_year + 1
            else:
                deadline_year = current_year
            
            deadline = date(deadline_year, deadline_month, deadline_day)
            
            # 如果截止日期已过，说明应该已发布
            if current_time.date() > deadline:
                missing_periods.append({
                    'period': next_period_date,
                    'type': self._get_period_type(next_period),
                    'deadline': deadline,
                    'priority': self.config.PERIOD_PRIORITY[next_period]
                })
            else:
                # 如果截止日期未到，停止检查
                break
        
        return missing_periods
    
    def _get_period_type(self, period_suffix: str) -> str:
        """获取期间类型"""
        types = {'0331': 'Q1', '0630': 'Q2', '0930': 'Q3', '1231': 'Q4'}
        return types[period_suffix]

class DataFetcher:
    """数据获取器 - 从Tushare获取财务数据"""
    
    def __init__(self, config: Config):
        self.config = config
        self.api = ts.pro_api(config.TUSHARE_TOKEN)
        logger.info("Tushare API 初始化成功")
    
    def fetch_financial_data(self, ts_code: str, company_type: str, report_date: str) -> Optional[pd.DataFrame]:
        """
        获取财务数据
        
        Args:
            ts_code: 股票代码
            company_type: 公司类型
            report_date: 报告期，如 '20240331'
            
        Returns:
            财务数据DataFrame或None
        """
        try:
            if company_type == '一般企业':
                return self._fetch_general_company_data(ts_code, report_date)
            elif company_type in ['商业银行', '证券公司', '保险公司']:
                return self._fetch_financial_company_data(ts_code, report_date, company_type)
            else:
                logger.warning(f"未知公司类型: {company_type}")
                return None
                
        except Exception as e:
            logger.error(f"获取 {ts_code} {report_date} 数据失败: {e}")
            return None
    
    def _fetch_general_company_data(self, ts_code: str, report_date: str) -> pd.DataFrame:
        """获取一般企业财务数据"""
        # 获取三大报表数据
        income_data = self.api.income(ts_code=ts_code, period=report_date)
        balance_data = self.api.balancesheet(ts_code=ts_code, period=report_date)
        cashflow_data = self.api.cashflow(ts_code=ts_code, period=report_date)
        
        time.sleep(self.config.API_DELAY)
        
        # 合并数据
        merged_data = self._merge_financial_statements(income_data, balance_data, cashflow_data)
        return merged_data
    
    def _fetch_financial_company_data(self, ts_code: str, report_date: str, company_type: str) -> pd.DataFrame:
        """获取金融公司财务数据（需要VIP权限）"""
        try:
            # 尝试使用VIP接口
            income_data = self.api.income_vip(ts_code=ts_code, period=report_date)
            balance_data = self.api.balancesheet_vip(ts_code=ts_code, period=report_date)
            cashflow_data = self.api.cashflow_vip(ts_code=ts_code, period=report_date)
            
            time.sleep(self.config.API_DELAY)
            
            merged_data = self._merge_financial_statements(income_data, balance_data, cashflow_data)
            return merged_data
            
        except Exception as e:
            logger.warning(f"VIP接口获取失败，尝试基础接口: {e}")
            # 降级到基础接口
            return self._fetch_general_company_data(ts_code, report_date)
    
    def _merge_financial_statements(self, income_df: pd.DataFrame, balance_df: pd.DataFrame, 
                                  cashflow_df: pd.DataFrame) -> pd.DataFrame:
        """合并三大财务报表"""
        if income_df.empty and balance_df.empty and cashflow_df.empty:
            return pd.DataFrame()
        
        # 以利润表为基础进行合并
        base_df = income_df if not income_df.empty else balance_df if not balance_df.empty else cashflow_df
        
        # 合并逻辑（简化版，实际需要根据字段映射）
        merged = base_df.copy()
        
        # 这里需要实现具体的字段映射逻辑
        # 暂时返回基础数据
        return merged

class MissingDataRecovery:
    """缺失数据恢复器"""

    def __init__(self, config: Config, data_fetcher: DataFetcher, time_manager: TimeManager, classifier: StockClassifier):
        self.config = config
        self.fetcher = data_fetcher
        self.time_manager = time_manager
        self.classifier = classifier
        self.recovery_stats = {
            'total_stocks': 0,
            'total_missing_periods': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'errors': []
        }

    def scan_all_stocks(self) -> Dict:
        """扫描所有股票，生成补齐计划"""
        logger.info("🔍 开始扫描所有股票文件...")

        if not os.path.exists(self.config.DATA_PATH):
            logger.error(f"数据目录不存在: {self.config.DATA_PATH}")
            return {}

        stock_folders = [d for d in os.listdir(self.config.DATA_PATH)
                        if os.path.isdir(os.path.join(self.config.DATA_PATH, d))]

        self.recovery_stats['total_stocks'] = len(stock_folders)
        logger.info(f"📁 发现 {len(stock_folders)} 个股票文件夹")

        recovery_plan = {}

        for i, stock_folder in enumerate(stock_folders):
            if i % 100 == 0:
                logger.info(f"📊 扫描进度: {i}/{len(stock_folders)}")

            try:
                # 查找CSV文件
                csv_files = glob.glob(f"{self.config.DATA_PATH}/{stock_folder}/*.csv")

                if not csv_files:
                    logger.warning(f"⚠️  {stock_folder} 文件夹中没有CSV文件")
                    continue

                csv_file = csv_files[0]  # 假设每个文件夹只有一个CSV

                # 获取最新数据日期
                latest_date = self.time_manager.get_latest_date_from_csv(csv_file)

                if latest_date is None:
                    logger.warning(f"⚠️  无法从 {csv_file} 获取最新日期")
                    continue

                # 检测缺失期间
                missing_periods = self.time_manager.detect_missing_periods(latest_date)

                if missing_periods:
                    # 从文件名推断公司类型
                    company_type = self.classifier.classify_from_filename(os.path.basename(csv_file))

                    recovery_plan[stock_folder] = {
                        'current_latest': latest_date,
                        'missing': missing_periods,
                        'csv_file': csv_file,
                        'company_type': company_type
                    }

                    self.recovery_stats['total_missing_periods'] += len(missing_periods)

            except Exception as e:
                logger.error(f"❌ 处理 {stock_folder} 时出错: {e}")
                self.recovery_stats['errors'].append(f"{stock_folder}: {str(e)}")

        logger.info(f"📊 扫描完成，发现 {len(recovery_plan)} 只股票需要补齐数据")
        logger.info(f"📊 总缺失期间数: {self.recovery_stats['total_missing_periods']}")

        return recovery_plan

    def execute_recovery_plan(self, recovery_plan: Dict, max_stocks: int = None):
        """执行补齐计划"""
        logger.info("🚀 开始执行数据补齐...")

        if max_stocks:
            # 限制处理的股票数量（用于测试）
            items = list(recovery_plan.items())[:max_stocks]
            recovery_plan = dict(items)
            logger.info(f"🔬 测试模式：只处理前 {max_stocks} 只股票")

        total_stocks = len(recovery_plan)

        for i, (stock_code, plan) in enumerate(recovery_plan.items()):
            logger.info(f"📈 [{i+1}/{total_stocks}] 处理 {stock_code}...")

            try:
                # 转换股票代码格式
                ts_code = self._convert_to_ts_code(stock_code)

                # 补齐缺失数据
                recovered_data = self._recover_stock_data(ts_code, plan)

                if recovered_data:
                    # 更新CSV文件
                    self._update_csv_file(plan['csv_file'], recovered_data)
                    logger.info(f"✅ {stock_code} 补齐完成")
                else:
                    logger.warning(f"⚠️  {stock_code} 没有获取到新数据")

            except Exception as e:
                logger.error(f"❌ 处理 {stock_code} 失败: {e}")
                self.recovery_stats['errors'].append(f"{stock_code}: {str(e)}")

        self._print_recovery_report()

    def _convert_to_ts_code(self, stock_code: str) -> str:
        """将股票代码转换为Tushare格式"""
        # sz300410 -> 300410.SZ
        # sh600000 -> 600000.SH
        if stock_code.startswith('sz'):
            return f"{stock_code[2:]}.SZ"
        elif stock_code.startswith('sh'):
            return f"{stock_code[2:]}.SH"
        else:
            # 如果已经是标准格式，直接返回
            return stock_code

    def _recover_stock_data(self, ts_code: str, plan: Dict) -> List[Dict]:
        """为单个股票恢复缺失数据"""
        missing_periods = plan['missing']
        company_type = plan['company_type']

        # 按优先级排序（年报优先）
        sorted_periods = sorted(missing_periods, key=lambda x: x['priority'], reverse=True)

        recovered_data = []

        for period_info in sorted_periods:
            period = period_info['period']
            period_type = period_info['type']

            try:
                logger.info(f"   📥 获取 {period} ({period_type}) 数据...")

                # 获取数据
                data = self.fetcher.fetch_financial_data(ts_code, company_type, period)

                if data is not None and not data.empty:
                    recovered_data.append({
                        'period': period,
                        'data': data,
                        'type': period_type
                    })
                    logger.info(f"   ✅ {period} 数据获取成功")
                    self.recovery_stats['successful_recoveries'] += 1
                else:
                    logger.warning(f"   ❌ {period} 数据为空或不存在")
                    self.recovery_stats['failed_recoveries'] += 1

                # API调用间隔
                time.sleep(self.config.API_DELAY)

            except Exception as e:
                error_msg = f"{ts_code}-{period}: {str(e)}"
                logger.error(f"   ❌ {period} 获取失败: {e}")
                self.recovery_stats['errors'].append(error_msg)
                self.recovery_stats['failed_recoveries'] += 1

        return recovered_data

    def _update_csv_file(self, csv_file_path: str, recovered_data: List[Dict]):
        """更新CSV文件"""
        if not recovered_data:
            return

        try:
            # 备份原文件
            backup_path = csv_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(csv_file_path, backup_path)
            logger.info(f"   💾 原文件已备份到: {backup_path}")

            # 读取原始数据
            original_df = pd.read_csv(csv_file_path, skiprows=1)

            # 添加新数据
            for recovery in recovered_data:
                new_row = self._convert_to_csv_format(recovery['data'], recovery['period'])
                if new_row is not None:
                    original_df = pd.concat([original_df, new_row], ignore_index=True)

            # 按报告期排序
            original_df = original_df.sort_values('report_date')

            # 保存更新后的文件
            with open(csv_file_path, 'w', encoding='utf-8') as f:
                # 写入第一行说明
                f.write("数据由邢不行整理，对数据字段有疑问的，可以直接微信私信邢不行，微信号：xbx297" + "," * 300 + "\n")
                # 写入数据
                original_df.to_csv(f, index=False)

            logger.info(f"   💾 CSV文件已更新")

        except Exception as e:
            logger.error(f"   ❌ 更新CSV文件失败: {e}")

    def _convert_to_csv_format(self, tushare_data: pd.DataFrame, period: str) -> Optional[pd.DataFrame]:
        """将Tushare数据转换为CSV格式"""
        # 这里需要实现具体的字段映射逻辑
        # 暂时返回简化版本
        if tushare_data.empty:
            return None

        # 创建基础行数据
        row_data = {
            'stock_code': tushare_data.iloc[0].get('ts_code', ''),
            'statement_format': '一般企业',  # 需要根据实际情况调整
            'report_date': period,
            'publish_date': tushare_data.iloc[0].get('ann_date', ''),
            '抓取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 添加其他字段（需要根据实际字段映射）
        # 这里只是示例，实际需要完整的字段映射

        return pd.DataFrame([row_data])

    def _print_recovery_report(self):
        """打印恢复报告"""
        stats = self.recovery_stats
        success_rate = (stats['successful_recoveries'] /
                       (stats['successful_recoveries'] + stats['failed_recoveries']) * 100
                       if (stats['successful_recoveries'] + stats['failed_recoveries']) > 0 else 0)

        logger.info("📊 数据补齐完成统计:")
        logger.info(f"   总股票数: {stats['total_stocks']}")
        logger.info(f"   总缺失期间数: {stats['total_missing_periods']}")
        logger.info(f"   成功补齐: {stats['successful_recoveries']} 期间")
        logger.info(f"   失败: {stats['failed_recoveries']} 期间")
        logger.info(f"   成功率: {success_rate:.1f}%")

        if stats['errors']:
            logger.info(f"   错误数: {len(stats['errors'])}")
            # 显示前5个错误
            for error in stats['errors'][:5]:
                logger.error(f"     {error}")
            if len(stats['errors']) > 5:
                logger.info(f"     ... 还有 {len(stats['errors']) - 5} 个错误")

if __name__ == "__main__":
    print("🚀 财务数据更新系统启动...")

    # 初始化配置
    config = Config()

    # 测试API连接
    try:
        api = ts.pro_api(config.TUSHARE_TOKEN)
        test_data = api.stock_basic(list_status='L', fields='ts_code,symbol,name')
        print(f"✅ Tushare API 连接成功，共获取到 {len(test_data)} 只股票")
    except Exception as e:
        print(f"❌ Tushare API 连接失败: {e}")
        sys.exit(1)

    print("📊 系统初始化完成，准备开始数据更新...")

    # 初始化各个组件
    data_fetcher = DataFetcher(config)
    time_manager = TimeManager(config)
    classifier = StockClassifier(data_fetcher.api)
    recovery_system = MissingDataRecovery(config, data_fetcher, time_manager, classifier)

    # 扫描并生成恢复计划
    recovery_plan = recovery_system.scan_all_stocks()

    if recovery_plan:
        print(f"📋 发现 {len(recovery_plan)} 只股票需要补齐数据")

        # 询问是否继续
        response = input("是否开始补齐数据？(y/n): ")
        if response.lower() == 'y':
            # 测试模式：只处理前10只股票
            recovery_system.execute_recovery_plan(recovery_plan, max_stocks=10)
        else:
            print("❌ 用户取消操作")
    else:
        print("✅ 所有股票数据都是最新的，无需补齐")
