#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射配置
将Tushare API返回的字段映射到现有CSV文件格式
"""

class FieldMapper:
    """字段映射器"""
    
    def __init__(self):
        # 基础字段映射
        self.base_mapping = {
            'ts_code': 'stock_code',
            'ann_date': 'publish_date',
            'end_date': 'report_date',
            'f_ann_date': 'publish_date'
        }
        
        # 一般企业字段映射
        self.general_company_mapping = {
            # 资产负债表 - 资产部分
            'total_assets': 'B_total_assets@xbx',
            'total_cur_assets': 'B_total_current_assets@xbx',
            'money_cap': 'B_currency_fund@xbx',
            'trad_asset': 'B_tradable_fnncl_assets@xbx',
            'notes_receiv': 'B_bill_receivable@xbx',
            'accounts_receiv': 'B_account_receivable@xbx',
            'oth_receiv': 'B_other_receivables@xbx',
            'prepayment': 'B_prepays@xbx',
            'div_receiv': 'B_dividend_receivable@xbx',
            'int_receiv': 'B_interest_receivable@xbx',
            'inventories': 'B_inventory@xbx',
            'amor_exp': 'B_lt_deferred_expense@xbx',
            'nca_within_1y': 'B_noncurrent_asset_due_within1y@xbx',
            'sett_rsrv': 'B_settle_reserves@xbx',
            'loanto_oth_bank_fi': 'B_lending_fund@xbx',
            'premium_receiv': 'B_premium_receivable@xbx',
            'reinsur_receiv': 'B_rein_account_receivable@xbx',
            'reinsur_res_receiv': 'B_rein_contract_reserve@xbx',
            'pur_resale_fa': 'B_buy_resale_fnncl_assets@xbx',
            'oth_cur_assets': 'B_other_cunrren_assets@xbx',
            
            # 非流动资产
            'total_nca': 'B_total_noncurrent_assets@xbx',
            'fa_avail_for_sale': 'B_saleable_finacial_assets@xbx',
            'htm_invest': 'B_held_to_maturity_invest@xbx',
            'lt_eqt_invest': 'B_lt_equity_invest@xbx',
            'invest_real_estate': 'B_invest_property@xbx',
            'time_deposits': 'B_fixed_deposit@xbx',
            'oth_assets': 'B_other_assets@xbx',
            'lt_rec': 'B_lt_receivable@xbx',
            'fix_assets': 'B_fixed_asset@xbx',
            'cip': 'B_construction_in_process@xbx',
            'const_materials': 'B_project_goods_and_material@xbx',
            'fixed_assets_disp': 'B_fixed_assets_disposal@xbx',
            'produc_bio_assets': 'B_productive_biological_assets@xbx',
            'oil_and_gas_assets': 'B_oil_and_gas_asset@xbx',
            'intan_assets': 'B_intangible_assets@xbx',
            'r_and_d': 'B_dev_expenditure@xbx',
            'goodwill': 'B_goodwill@xbx',
            'lt_amor_exp': 'B_lt_deferred_expense@xbx',
            'defer_tax_assets': 'B_dt_assets@xbx',
            'oth_nca': 'B_othr_noncurrent_assets@xbx',
            
            # 负债部分
            'total_liab': 'B_total_liab@xbx',
            'total_cur_liab': 'B_total_current_liab@xbx',
            'st_borr': 'B_st_borrow@xbx',
            'trad_liab': 'B_tradable_fnncl_liab@xbx',
            'notes_payable': 'B_bill_payable@xbx',
            'acct_payable': 'B_accounts_payable@xbx',
            'adv_receipts': 'B_advance_payment@xbx',
            'payroll_payable': 'B_payroll_payable@xbx',
            'taxes_payable': 'B_tax_payable@xbx',
            'int_payable': 'B_interest_payable@xbx',
            'div_payable': 'B_dividend_payable@xbx',
            'oth_payable': 'B_other_payables@xbx',
            'acc_exp': 'B_accrued_payables@xbx',
            'deferred_inc': 'B_differed_income_current_liab@xbx',
            'st_bonds_payable': 'B_st_bond_payable@xbx',
            'payable_to_reinsurer': 'B_rein_payable@xbx',
            'rsrv_insur_cont': 'B_insurance_contract_reserve@xbx',
            'acting_trading_sec': 'B_acting_td_sec@xbx',
            'acting_uw_sec': 'B_act_underwriting_sec@xbx',
            'non_cur_liab_due_1y': 'B_noncurrent_liab_due_in1y@xbx',
            'oth_cur_liab': 'B_other_current_liab@xbx',
            
            # 非流动负债
            'total_ncl': 'B_total_noncurrent_liab@xbx',
            'lt_borr': 'B_lt_loan@xbx',
            'bonds_payable': 'B_bond_payable@xbx',
            'lt_payable': 'B_lt_payable@xbx',
            'specific_payables': 'B_special_payable@xbx',
            'estimated_liab': 'B_estimated_liab@xbx',
            'defer_tax_liab': 'B_dt_liab@xbx',
            'defer_inc_non_cur_liab': 'B_differed_incomencl@xbx',
            'oth_ncl': 'B_othr_noncurrent_liab@xbx',
            
            # 所有者权益
            'total_hldr_eqy_exc_min_int': 'B_total_equity_atoopc@xbx',
            'total_hldr_eqy_inc_min_int': 'B_total_owner_equity@xbx',
            'paid_cap': 'B_actual_received_capital@xbx',
            'cap_rese': 'B_capital_reserve@xbx',
            'treas_shr': 'B_treasury@xbx',
            'oth_eqt_tools': 'B_other_equity_instruments@xbx',
            'oth_eqt_tools_p_shr': 'B_preferred_shares@xbx',
            'surplus_rese': 'B_earned_surplus@xbx',
            'ordin_risk_reser': 'B_general_risk_provision@xbx',
            'retained_earnings': 'B_undstrbtd_profit@xbx',
            'trad_asset_diff': 'B_frgn_currency_convert_diff@xbx',
            'invest_loss_unconf': 'B_bs_other_compre_income@xbx',
            'minority_int': 'B_minority_equity@xbx',
            
            # 利润表
            'total_revenue': 'R_operating_total_revenue@xbx',
            'revenue': 'R_revenue@xbx',
            'int_income': 'R_interest_income@xbx',
            'prem_earned': 'R_earned_premium@xbx',
            'comm_income': 'R_fee_and_commi_income@xbx',
            'n_commis_income': 'R_commi_net_income@xbx',
            'n_oth_income': 'R_other_income@xbx',
            'n_oth_b_income': 'R_other_bussiness_income@xbx',
            'prem_income': 'R_insurance_income@xbx',
            'out_prem': 'R_ceded_out_premium@xbx',
            'une_prem_reser': 'R_draw_undue_duty_deposit@xbx',
            'reins_income': 'R_rein_premium_income@xbx',
            'n_sec_tb_income': 'R_net_income_from_invest_banking@xbx',
            'n_sec_uw_income': 'R_uw_income@xbx',
            'n_asset_mg_income': 'R_ams_charge_net_income@xbx',
            'oth_b_income': 'R_other_bussiness_income@xbx',
            'fv_value_chg_gain': 'R_fv_chg_income@xbx',
            'invest_income': 'R_invest_income@xbx',
            'ass_invest_income': 'R_ii_from_jc_etc@xbx',
            'forex_gain': 'R_exchange_gain@xbx',
            'total_cogs': 'R_operating_total_cost@xbx',
            'oper_cost': 'R_operating_cost@xbx',
            'int_exp': 'R_interest_payout@xbx',
            'comm_exp': 'R_charge_and_commi_expenses@xbx',
            'biz_tax_surchg': 'R_operating_taxes_and_surcharge@xbx',
            'sell_exp': 'R_sales_fee@xbx',
            'admin_exp': 'R_manage_fee@xbx',
            'fin_exp': 'R_financing_expenses@xbx',
            'assets_impair_loss': 'R_asset_impairment_loss@xbx',
            'prem_refund': 'R_refunded_premium@xbx',
            'compens_payout': 'R_compensate_net_pay@xbx',
            'reser_insur_liab': 'R_extract_ic_reserve_net_amt@xbx',
            'div_payt': 'R_commi_on_insurance_policy@xbx',
            'reins_exp': 'R_rein_expenditure@xbx',
            'oper_exp': 'R_operating_payout@xbx',
            'compens_payout_refu': 'R_amortized_reimburse_cost@xbx',
            'insur_reser_refu': 'R_extract_ic_reserve@xbx',
            'reins_cost_refund': 'R_amortized_rein_expenditure@xbx',
            'other_bus_cost': 'R_other_business_costs@xbx',
            'operate_profit': 'R_op@xbx',
            'non_oper_income': 'R_non_operating_income@xbx',
            'non_oper_exp': 'R_nonoperating_cost@xbx',
            'nca_disploss': 'R_noncurrent_asset_dispose_loss@xbx',
            'total_profit': 'R_total_profit@xbx',
            'income_tax': 'R_income_tax_cost@xbx',
            'n_income': 'R_np@xbx',
            'n_income_attr_p': 'R_np_atoopc@xbx',
            'minority_gain': 'R_minority_gal@xbx',
            'oth_compr_income': 'R_othrcompre_income_atoopc@xbx',
            't_compr_income': 'R_total_compre_income@xbx',
            'compr_inc_attr_p': 'R_total_compre_income_atsopc@xbx',
            'compr_inc_attr_m_s': 'R_total_compre_income_atms@xbx',
            'ebit': 'R_ebit@xbx',
            'ebitda': 'R_ebitda@xbx',
            'insurance_exp': 'R_insurance_exp@xbx',
            'undist_profit': 'R_undist_profit@xbx',
            'distable_profit': 'R_distable_profit@xbx',
            'rd_exp': 'R_rad_cost_sum@xbx',
            
            # 现金流量表
            'c_fr_sale_sg': 'C_cash_received_of_sales_service@xbx',
            'recp_tax_rends': 'C_refund_of_tax_and_levies@xbx',
            'n_depos_incr_fi': 'C_deposit_and_interbank_net_add@xbx',
            'n_incr_loans_cb': 'C_borrowing_net_add_central_bank@xbx',
            'n_inc_borr_oth_fi': 'C_lending_net_add_other_org@xbx',
            'prem_fr_orig_contr': 'C_cash_received_from_orig_ic@xbx',
            'n_incr_insured_dep': 'C_naaassured_saving_and_invest@xbx',
            'n_reinsur_prem': 'C_net_cash_received_from_rein@xbx',
            'n_incr_disp_tfa': 'C_naa_of_disposal_fnncl_assets@xbx',
            'ifc_cash_incr': 'C_cash_received_of_interest_etc@xbx',
            'n_incr_disp_faas': 'C_naa_of_disposal_fnncl_assets@xbx',
            'n_incr_loans_oth_bank': 'C_borrowing_net_increase_amt@xbx',
            'n_cap_incr_repur': 'C_net_add_in_repur_capital@xbx',
            'c_fr_oth_operate_a': 'C_cash_received_of_other_oa@xbx',
            'c_inf_fr_operate_a': 'C_sub_total_of_ci_from_oa@xbx',
            'c_paid_goods_s': 'C_goods_buy_and_service_cash_pay@xbx',
            'c_paid_to_for_empl': 'C_cash_paid_to_staff_etc@xbx',
            'c_paid_for_taxes': 'C_payments_of_all_taxes@xbx',
            'n_incr_clt_loan_adv': 'C_loan_and_advancenet_add@xbx',
            'n_incr_dep_cbob': 'C_naa_of_cb_and_interbank@xbx',
            'c_pay_claims_orig_inco': 'C_cash_of_orig_ic_indemnity@xbx',
            'pay_handling_chrg': 'C_cash_paid_for_interests_etc@xbx',
            'pay_comm_insur_plcy': 'C_cash_paid_for_pd@xbx',
            'c_paid_oth_operate_a': 'C_other_cash_paid_related_to_oa@xbx',
            'c_outf_fr_operate_a': 'C_sub_total_of_cos_from_oa@xbx',
            'n_cashflow_act': 'C_ncf_from_oa@xbx',
            
            # 投资活动现金流
            'c_disp_withdrwl_invest': 'C_cash_received_of_dspsl_invest@xbx',
            'c_recp_return_invest': 'C_invest_income_cash_received@xbx',
            'n_recp_disp_fiolta': 'C_net_cash_of_disposal_assets@xbx',
            'n_recp_disp_sobu': 'C_net_cash_of_disposal_branch@xbx',
            'c_recp_oth_invest': 'C_cash_received_of_other_fa@xbx',
            'c_inf_fr_inv_act': 'C_sub_total_of_ci_from_ia@xbx',
            'c_pay_acq_const_fiolta': 'C_cash_paid_for_assets@xbx',
            'c_paid_invest': 'C_invest_paid_cash@xbx',
            'n_incr_pledge_loan': 'C_net_add_in_pledge_loans@xbx',
            'n_pay_acq_sobu': 'C_net_cash_amt_from_branch@xbx',
            'c_pay_oth_inv_act': 'C_other_cash_paid_related_to_ia@xbx',
            'c_outf_fr_inv_act': 'C_sub_total_of_cos_from_ia@xbx',
            'n_cashflow_inv_act': 'C_ncf_from_ia@xbx',
            
            # 筹资活动现金流
            'c_recp_borrow': 'C_cash_received_of_borrowing@xbx',
            'proc_issue_bonds': 'C_cash_received_from_bond_issue@xbx',
            'c_recp_oth_fin_act': 'C_cash_received_of_othr_fa@xbx',
            'c_inf_fr_fin_act': 'C_sub_total_of_ci_from_fa@xbx',
            'free_cashflow': 'C_ncf_from_fa@xbx',
            'c_prepay_amt_borr': 'C_cash_pay_for_debt@xbx',
            'c_pay_dist_dpcp_int_exp': 'C_cash_paid_of_distribution@xbx',
            'prcp_pay_socp': 'C_dap_paid_to_minority_holder@xbx',
            'c_pay_oth_fin_act': 'C_othrcash_paid_relating_to_fa@xbx',
            'c_outf_fr_fin_act': 'C_sub_total_of_cos_from_fa@xbx',
            'n_cash_flows_fnc_act': 'C_ncf_from_fa@xbx',
            'eff_fx_flu_cash': 'C_effect_of_exchange_chg_on_cce@xbx',
            'n_incr_cash_cash_equ': 'C_net_increase_in_cce@xbx',
            'c_cash_equ_beg_period': 'C_initial_cce_balance@xbx',
            'c_cash_equ_end_period': 'C_final_balance_of_cce@xbx'
        }
        
        # 银行业特有字段映射
        self.bank_mapping = {
            'cash_reser_cb': 'B_central_bank_cash_and_deposit@xbx',
            'depos_oth_bfi': 'B_interbank_storage@xbx',
            'prec_metals': 'B_precious_metal@xbx',
            'oth_assets': 'B_other_assets@xbx',
            'total_deposits': 'B_savings_absorption@xbx',
            'ib_deposits': 'B_interbank_deposit_etc@xbx',
            'oth_liab': 'B_other_liab@xbx',
            'net_int_income': 'R_interest_net_income@xbx',
            'net_comm_income': 'R_commi_net_income@xbx',
            'oth_bus_income': 'R_other_bussiness_income@xbx'
        }
        
        # 证券公司特有字段映射
        self.securities_mapping = {
            'client_depos': 'B_customer_deposit@xbx',
            'client_prov': 'B_customer_provision@xbx',
            'seat_fees': 'B_trans_seat_fee@xbx',
            'net_broker_income': 'R_brokerage_charge_net_income@xbx',
            'net_uw_income': 'R_net_income_from_invest_banking@xbx',
            'net_am_income': 'R_ams_charge_net_income@xbx'
        }
        
        # 保险公司特有字段映射
        self.insurance_mapping = {
            'insured_pledge_loan': 'B_assured_pledge_loan@xbx',
            'cap_depos': 'B_paid_capital_deposit@xbx',
            'indep_acct_assets': 'B_separate_account@xbx',
            'incl_seat_fees_exchanges': 'B_trans_seat_fee@xbx',
            'r_int_exp': 'R_interest_payout@xbx',
            'net_earned_prem': 'R_earned_premium@xbx',
            'net_invest_inc': 'R_invest_income@xbx',
            'prem_earned': 'R_earned_premium@xbx',
            'ceded_prem': 'R_ceded_out_premium@xbx',
            'chg_unearned_prem_res': 'R_draw_undue_duty_deposit@xbx'
        }
    
    def get_mapping_for_company_type(self, company_type: str) -> dict:
        """根据公司类型获取字段映射"""
        base = self.base_mapping.copy()
        base.update(self.general_company_mapping)
        
        if company_type == '商业银行':
            base.update(self.bank_mapping)
        elif company_type == '证券公司':
            base.update(self.securities_mapping)
        elif company_type == '保险公司':
            base.update(self.insurance_mapping)
        
        return base
    
    def map_tushare_to_csv(self, tushare_data: dict, company_type: str) -> dict:
        """将Tushare数据映射到CSV格式"""
        mapping = self.get_mapping_for_company_type(company_type)
        csv_data = {}
        
        for tushare_field, csv_field in mapping.items():
            if tushare_field in tushare_data:
                csv_data[csv_field] = tushare_data[tushare_field]
        
        return csv_data
