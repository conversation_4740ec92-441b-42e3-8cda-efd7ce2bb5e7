#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版财务数据更新器 - 用于测试和演示
"""

import os
import pandas as pd
import tushare as ts
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleUpdater:
    def __init__(self):
        self.token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
        self.data_path = "G:/i/stock/stock_data/stock-fin-data-xbx"
        self.api = ts.pro_api(self.token)
        
    def read_csv_with_encoding(self, file_path):
        """尝试不同编码读取CSV文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, skiprows=1, encoding=encoding)
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.error(f"读取文件失败 {file_path}: {e}")
                break
        
        return None
    
    def get_latest_date(self, csv_file):
        """获取CSV文件中的最新报告期"""
        df = self.read_csv_with_encoding(csv_file)
        if df is not None and 'report_date' in df.columns and not df.empty:
            return int(df['report_date'].max())
        return None
    
    def should_update_period(self, period_str):
        """判断某个报告期是否应该已经发布"""
        current_date = datetime.now()
        year = int(period_str[:4])
        month_day = period_str[4:]
        
        # 报告期发布时间表
        deadlines = {
            '0331': (year, 4, 30),      # 一季报：4月30日
            '0630': (year, 8, 31),      # 半年报：8月31日
            '0930': (year, 10, 31),     # 三季报：10月31日
            '1231': (year + 1, 4, 30)   # 年报：次年4月30日
        }
        
        if month_day in deadlines:
            deadline_year, deadline_month, deadline_day = deadlines[month_day]
            deadline = datetime(deadline_year, deadline_month, deadline_day)
            return current_date > deadline
        
        return False
    
    def get_missing_periods(self, latest_date):
        """获取缺失的报告期"""
        if latest_date is None:
            return []
        
        latest_year = int(str(latest_date)[:4])
        latest_period = str(latest_date)[4:]
        
        periods = ['0331', '0630', '0930', '1231']
        missing = []
        
        # 从最新期间开始检查后续期间
        current_year = latest_year
        current_idx = periods.index(latest_period)
        
        for _ in range(8):  # 最多检查8个期间（2年）
            current_idx += 1
            if current_idx >= len(periods):
                current_idx = 0
                current_year += 1
            
            next_period = f"{current_year}{periods[current_idx]}"
            
            if self.should_update_period(next_period):
                missing.append(next_period)
            else:
                break
        
        return missing
    
    def convert_stock_code(self, folder_name):
        """将文件夹名转换为Tushare股票代码"""
        if folder_name.startswith('sz'):
            return f"{folder_name[2:]}.SZ"
        elif folder_name.startswith('sh'):
            return f"{folder_name[2:]}.SH"
        elif folder_name.startswith('bj'):
            return f"{folder_name[2:]}.BJ"
        return folder_name
    
    def fetch_data(self, ts_code, period):
        """获取财务数据"""
        try:
            logger.info(f"   获取 {ts_code} {period} 数据...")
            
            # 获取利润表数据
            income_data = self.api.income(ts_code=ts_code, period=period)
            
            if not income_data.empty:
                logger.info(f"   ✅ 成功获取 {period} 数据")
                return income_data
            else:
                logger.warning(f"   ⚠️  {period} 数据为空")
                return None
                
        except Exception as e:
            logger.error(f"   ❌ 获取 {period} 数据失败: {e}")
            return None
    
    def test_few_stocks(self, max_stocks=5):
        """测试少量股票的更新"""
        logger.info(f"🔍 开始测试前 {max_stocks} 只股票...")
        
        if not os.path.exists(self.data_path):
            logger.error(f"数据目录不存在: {self.data_path}")
            return
        
        stock_folders = [d for d in os.listdir(self.data_path) 
                        if os.path.isdir(os.path.join(self.data_path, d))][:max_stocks]
        
        results = []
        
        for i, folder in enumerate(stock_folders):
            logger.info(f"📊 [{i+1}/{len(stock_folders)}] 处理 {folder}...")
            
            try:
                # 查找CSV文件
                csv_files = [f for f in os.listdir(os.path.join(self.data_path, folder)) 
                            if f.endswith('.csv')]
                
                if not csv_files:
                    logger.warning(f"   ⚠️  {folder} 中没有CSV文件")
                    continue
                
                csv_path = os.path.join(self.data_path, folder, csv_files[0])
                
                # 获取最新日期
                latest_date = self.get_latest_date(csv_path)
                
                if latest_date is None:
                    logger.warning(f"   ⚠️  无法读取 {folder} 的最新日期")
                    continue
                
                # 检查缺失期间
                missing_periods = self.get_missing_periods(latest_date)
                
                result = {
                    'folder': folder,
                    'latest_date': latest_date,
                    'missing_periods': missing_periods,
                    'csv_file': csv_path
                }
                
                if missing_periods:
                    logger.info(f"   📅 最新: {latest_date}, 缺失: {missing_periods}")
                    
                    # 尝试获取第一个缺失期间的数据
                    ts_code = self.convert_stock_code(folder)
                    first_missing = missing_periods[0]
                    
                    data = self.fetch_data(ts_code, first_missing)
                    result['test_data'] = data is not None
                    
                else:
                    logger.info(f"   ✅ 最新: {latest_date}, 无缺失")
                    result['test_data'] = None
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"   ❌ 处理 {folder} 失败: {e}")
        
        # 输出总结
        logger.info("\n📊 测试总结:")
        total_stocks = len(results)
        stocks_with_missing = len([r for r in results if r['missing_periods']])
        successful_fetches = len([r for r in results if r.get('test_data') == True])
        
        logger.info(f"   总股票数: {total_stocks}")
        logger.info(f"   有缺失数据: {stocks_with_missing}")
        logger.info(f"   成功获取数据: {successful_fetches}")
        
        return results
    
    def update_single_stock(self, folder_name):
        """更新单只股票的数据"""
        print(f"🎯 开始更新单只股票: {folder_name}")
        logger.info(f"🎯 开始更新单只股票: {folder_name}")

        folder_path = os.path.join(self.data_path, folder_name)
        print(f"📁 检查文件夹: {folder_path}")

        if not os.path.exists(folder_path):
            print(f"❌ 股票文件夹不存在: {folder_path}")
            logger.error(f"股票文件夹不存在: {folder_path}")
            return False
        
        # 查找CSV文件
        csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]
        print(f"📄 找到CSV文件: {csv_files}")

        if not csv_files:
            print(f"❌ 没有找到CSV文件")
            logger.error(f"没有找到CSV文件")
            return False

        csv_path = os.path.join(folder_path, csv_files[0])
        print(f"📊 读取文件: {csv_path}")

        # 获取最新日期和缺失期间
        latest_date = self.get_latest_date(csv_path)
        print(f"📅 最新日期: {latest_date}")

        if latest_date is None:
            print(f"❌ 无法读取最新日期")
            logger.error(f"无法读取最新日期")
            return False
        
        missing_periods = self.get_missing_periods(latest_date)
        print(f"📋 缺失期间: {missing_periods}")

        if not missing_periods:
            print(f"✅ 数据已是最新，无需更新")
            logger.info(f"✅ 数据已是最新，无需更新")
            return True

        print(f"📅 最新日期: {latest_date}")
        print(f"📋 需要更新的期间: {missing_periods}")
        logger.info(f"📅 最新日期: {latest_date}")
        logger.info(f"📋 需要更新的期间: {missing_periods}")

        # 转换股票代码
        ts_code = self.convert_stock_code(folder_name)
        print(f"📈 股票代码: {ts_code}")
        logger.info(f"📈 股票代码: {ts_code}")
        
        # 获取缺失数据
        new_data_list = []
        for period in missing_periods:
            data = self.fetch_data(ts_code, period)
            if data is not None:
                new_data_list.append((period, data))
        
        if new_data_list:
            logger.info(f"✅ 成功获取 {len(new_data_list)} 个期间的数据")
            logger.info("💾 数据获取完成（实际更新CSV文件的功能待完善）")
            return True
        else:
            logger.warning("⚠️  没有获取到新数据")
            return False

def main():
    print("🚀 简化版财务数据更新器启动...")
    
    updater = SimpleUpdater()
    
    # 测试API连接
    try:
        test_data = updater.api.stock_basic(list_status='L', fields='ts_code,symbol,name')
        print(f"✅ Tushare API 连接成功，共 {len(test_data)} 只股票")
    except Exception as e:
        print(f"❌ Tushare API 连接失败: {e}")
        return
    
    print("\n选择操作:")
    print("1. 测试前5只股票")
    print("2. 更新单只股票")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ")
    
    if choice == '1':
        updater.test_few_stocks(5)
    elif choice == '2':
        stock_code = input("请输入股票文件夹名 (如 sz300410): ")
        updater.update_single_stock(stock_code)
    elif choice == '3':
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
